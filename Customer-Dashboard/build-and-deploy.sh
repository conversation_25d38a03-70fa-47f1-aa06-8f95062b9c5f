#!/bin/bash

echo "===== Building and deploying UI updates ====="
echo

echo "===== Building frontend ====="
cd static/customer-dashboard-ui
# Install dependencies with legacy-peer-deps flag
npm install --legacy-peer-deps
# Build the frontend
npm run build
cd ../..
echo

echo "===== Deploying app ====="
forge deploy -e production
echo

echo "===== Deployment complete ====="
echo
echo "Please run the following command to check logs:"
echo "forge logs"
echo
