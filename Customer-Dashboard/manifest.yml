modules:
  jira:dashboardGadget:
    - key: epic-progress-tracker-gadget
      title: Customer Onboarding
      description: Tracks progress of customer onboarding by displaying tasks and completion status.
      thumbnail: https://cdn.theorg.com/a4309d50-7be3-4502-8d12-90d97debffd1_medium.jpg
      resource: main
      resolver:
        function: resolver
      edit:
        resource: main
      config:
        - name: filterId
          type: string
          title: Saved Filter
          description: Select a saved filter to show epics from that filter
          required: false
        - name: selectedEpicKey
          type: string
          title: Epic
          description: Select an epic to show only that specific epic (optional)
          required: false
        - name: showOverall
          type: boolean
          title: Show Overall Progress
          description: Display overall progress across all epics
          defaultValue: true
        - name: highlightColor
          type: string
          title: Highlight Color
          description: Choose a highlight color for the progress bars and UI elements
          defaultValue: "#0052CC"
        - name: uiStyle
          type: string
          title: UI Style
          description: Choose between a modern stylish UI or a classic Jira-like UI
          defaultValue: modern
        - name: csmColumnLabel
          type: string
          title: CSM Column Label
          description: Customize the label for the CSM Assigned column
          defaultValue: "CSM Assigned"
        - name: fundedByColumnLabel
          type: string
          title: Funded By Column Label
          description: Customize the label for the Funded By column
          defaultValue: "Funded By"
        - name: showFundedByColumn
          type: boolean
          title: Show Funded By Column
          description: Show or hide the Funded By column
          defaultValue: true
        - name: salesContactColumnLabel
          type: string
          title: Sales Contact Column Label
          description: Customize the label for the Sales Contact column
          defaultValue: "Sales Contact"
        - name: showSalesContactColumn
          type: boolean
          title: Show Sales Contact Column
          description: Show or hide the Sales Contact column
          defaultValue: true
        - name: showCsmColumn
          type: boolean
          title: Show CSM Column
          description: Show or hide the CSM Assigned column
          defaultValue: true
        - name: sreContactColumnLabel
          type: string
          title: SRE Contact Column Label
          description: Customize the label for the SRE Contact column
          defaultValue: "SRE Contact"
        - name: showSreContactColumn
          type: boolean
          title: Show SRE Contact Column
          description: Show or hide the SRE Contact column
          defaultValue: true
        - name: dueDateColumnLabel
          type: string
          title: Due Date Column Label
          description: Customize the label for the Due Date column
          defaultValue: "Due Date"
        - name: showDueDateColumn
          type: boolean
          title: Show Due Date Column
          description: Show or hide the Due Date column
          defaultValue: true
        - name: showTasksColumns
          type: boolean
          title: Show Tasks Columns
          description: Show or hide the All Tasks and Completed Tasks columns
          defaultValue: true
  function:
    - key: resolver
      handler: index.handler
resources:
  - key: main
    path: static/customer-dashboard-ui/build
permissions:
  content:
    styles:
      - unsafe-inline
  scopes:
    - read:jira-work
    - read:issue:jira
    - read:issue-details:jira
    - read:project:jira
app:
  runtime:
    name: nodejs22.x
  id: ari:cloud:ecosystem::app/2c0bed45-ae29-4661-96da-6b38aaa29811
