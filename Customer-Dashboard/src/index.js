import Resolver from '@forge/resolver';
import api, { route } from '@forge/api';

const resolver = new Resolver();

// Function to fetch saved filters
async function fetchSavedFilters() {
  try {
    console.log('Fetching saved filters');

    // First try to get favorite filters
    const favoriteResponse = await api.asUser().requestJira(route`/rest/api/3/filter/favourite`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const favoriteFilters = await favoriteResponse.json();
    console.log(`Found ${favoriteFilters.length} favorite filters`);

    // Then get my filters
    const myFiltersResponse = await api.asUser().requestJira(route`/rest/api/3/filter/my`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      query: {
        includeFavourites: false // Don't include favorites again
      }
    });

    const myFilters = await myFiltersResponse.json();
    console.log(`Found ${myFilters.length} additional personal filters`);

    // Combine both sets of filters
    const allFilters = [...favoriteFilters, ...myFilters];

    // Remove duplicates by filter ID
    const uniqueFilters = Array.from(
      new Map(allFilters.map(filter => [filter.id, filter])).values()
    );

    console.log(`Returning ${uniqueFilters.length} unique filters`);
    return uniqueFilters;
  } catch (error) {
    console.error('Error fetching saved filters:', error);
    return [];
  }
}

// Function to fetch issues from a filter with pagination and optimization
async function fetchIssuesFromFilter(filterId) {
  try {
    // Validate filterId
    if (!filterId) {
      console.error('No filter ID provided to fetchIssuesFromFilter');
      return [];
    }

    // Ensure filterId is a string and trim any whitespace or quotes
    const cleanFilterId = String(filterId).trim().replace(/^["']|["']$/g, '');
    console.log(`Fetching filter with ID: ${cleanFilterId}`);

    // Use route template literal correctly - don't construct the URL separately
    const filterResponse = await api.asUser().requestJira(route`/rest/api/3/filter/${cleanFilterId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`Before filterResponse when fetching filter ${filterId}:`, filterResponse);

    // Check if the response is valid
    if (!filterResponse.ok) {
      console.error(`Error filterResponse when fetching filter ${filterId}: HTTP ${filterResponse.status}`);
      return [];
    }

    // Get the response text first to check if it's valid JSON
    const responseText = await filterResponse.text();
    if (!responseText || responseText.trim() === '') {
      console.error(`Empty response when fetching filter ${filterId}`);
      return [];
    }

    // Try to parse the JSON
    let filter;
    try {
      filter = JSON.parse(responseText);
    } catch (jsonError) {
      console.error(`Error parsing JSON for filter ${filterId}:`, jsonError);
      console.error(`Response text: ${responseText.substring(0, 100)}...`);
      return [];
    }

    // Check if the filter has a JQL query
    if (!filter || !filter.jql) {
      console.error(`Filter ${filterId} does not have a JQL query`);
      return [];
    }

    console.log(`Filter ${filterId} JQL: ${filter.jql}`);

    // Optimize the fields we request to only what's needed
    const requiredFields = [
      'summary',
      'status',
      'issuetype',
      'assignee',
      'customfield_10298', // Funded By
      'customfield_10299', // Sales Contact
      'customfield_10300', // SRE Contact
      'duedate'           // Due Date
    ];

    // Implement pagination to avoid timeouts with large result sets
    let startAt = 0;
    const maxResults = 25; // Smaller batch size to avoid timeouts
    let allIssues = [];
    let totalIssues = 1; // Initial value to enter the loop

    // Increased limit to fetch more issues
    const maxIssuesToFetch = 200;

    // Use pagination to fetch issues in batches
    while (startAt < totalIssues && allIssues.length < maxIssuesToFetch) {
      console.log(`Fetching issues batch: startAt=${startAt}, maxResults=${maxResults}`);

      const searchResponse = await api.asUser().requestJira(route`/rest/api/3/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jql: filter.jql,
          startAt: startAt,
          maxResults: maxResults,
          fields: requiredFields
        })
      });

      // Check if the search response is valid
      if (!searchResponse.ok) {
        console.error(`Error searching issues with filter ${filterId}: HTTP ${searchResponse.status}`);
        break;
      }

      // Get the search response text first to check if it's valid JSON
      const searchResponseText = await searchResponse.text();
      if (!searchResponseText || searchResponseText.trim() === '') {
        console.error(`Empty response when searching issues with filter ${filterId}`);
        break;
      }

      // Try to parse the JSON
      let searchResults;
      try {
        searchResults = JSON.parse(searchResponseText);
      } catch (jsonError) {
        console.error(`Error parsing JSON for search results with filter ${filterId}:`, jsonError);
        console.error(`Response text: ${searchResponseText.substring(0, 100)}...`);
        break;
      }

      totalIssues = searchResults.total || 0;

      if (searchResults.issues && searchResults.issues.length > 0) {
        console.log(`Fetched batch of ${searchResults.issues.length} issues (total: ${totalIssues})`);
        allIssues = allIssues.concat(searchResults.issues);
      } else {
        console.log(`No issues in this batch or end of results`);
        break;
      }

      // Move to next batch
      startAt += maxResults;

      // If we've fetched enough issues, break to avoid timeout
      if (allIssues.length >= maxIssuesToFetch) {
        console.log(`Reached maximum issue limit (${maxIssuesToFetch}). Stopping pagination.`);
        break;
      }
    }

    console.log(`Total issues fetched: ${allIssues.length} out of ${totalIssues} total`);

    if (allIssues.length === 0) {
      console.log(`No issues found in filter ${filterId}`);
      return [];
    }

    // Log the first issue to see its structure
    if (allIssues.length > 0) {
      console.log('Sample issue structure:', JSON.stringify({
        key: allIssues[0].key,
        summary: allIssues[0].fields.summary,
        issuetype: allIssues[0].fields.issuetype ?
          allIssues[0].fields.issuetype.name : 'Unknown'
      }));
    }

    // Filter to only include epics with better error handling
    const epics = allIssues.filter(issue => {
      try {
        return issue &&
               issue.fields &&
               issue.fields.issuetype &&
               (issue.fields.issuetype.name === 'Epic' ||
                issue.fields.issuetype.name.toLowerCase().includes('epic'));
      } catch (err) {
        console.error(`Error checking if issue ${issue.key || 'unknown'} is an epic:`, err);
        return false;
      }
    });

    console.log(`Found ${epics.length} epics from filter ${filterId} (out of ${allIssues.length} total issues)`);

    // If no epics found, return a limited number of issues as fallback
    if (epics.length === 0 && allIssues.length > 0) {
      console.log(`No epics found in filter ${filterId}, returning up to 10 issues as fallback`);
      return allIssues.slice(0, 10); // Limit to 10 issues to avoid performance issues
    }

    // Process all epics returned by the filter
    // No limit on the number of epics to process
    console.log(`Processing all ${epics.length} epics from the filter`);

    return epics;
  } catch (error) {
    console.error(`Error fetching issues from filter ${filterId}:`, error);
    return [];
  }
}

// Function to fetch all epics or a specific epic
async function fetchEpics(projectKey, epicKey) {
  try {
    // If epicKey is provided, fetch only that specific epic
    if (epicKey && epicKey.trim() !== '') {
      console.log(`Fetching specific epic with key: ${epicKey}`);

      try {
        // First try to get the epic directly by key
        const response = await api.asUser().requestJira(route`/rest/api/3/issue/${epicKey}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          },
          query: {
            fields: 'summary,status,description,issuetype,assignee,customfield_10298,customfield_10299,customfield_10300,duedate'
          }
        });

        const epicData = await response.json();

        // Check if the issue exists and is an Epic
        if (epicData && epicData.fields) {
          // Check if it's an Epic by issue type
          if (epicData.fields.issuetype &&
              (epicData.fields.issuetype.name === 'Epic' ||
               epicData.fields.issuetype.name.toLowerCase().includes('epic'))) {
            console.log(`Successfully fetched epic: ${epicKey}`);
            return [epicData];
          } else {
            console.log(`Issue ${epicKey} is not an Epic. Issue type: ${epicData.fields.issuetype ? epicData.fields.issuetype.name : 'Unknown'}`);
          }
        } else {
          console.log(`Issue ${epicKey} could not be found or has no fields`);
        }

        // If we couldn't get it directly or it's not an Epic, try JQL as a fallback
        console.log(`Trying JQL to find epic with key: ${epicKey}`);
        const jqlResponse = await api.asUser().requestJira(route`/rest/api/3/search`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            jql: `key = "${epicKey}" AND issuetype = Epic`,
            maxResults: 1,
            fields: ['summary', 'status', 'description', 'issuetype', 'assignee', 'customfield_10298', 'customfield_10299', 'customfield_10300', 'duedate']
          })
        });

        const jqlData = await jqlResponse.json();
        if (jqlData.issues && jqlData.issues.length > 0) {
          console.log(`Found epic ${epicKey} using JQL`);
          return jqlData.issues;
        }

        console.log(`Epic ${epicKey} not found using any method`);
        return [];
      } catch (epicError) {
        console.error(`Error fetching specific epic ${epicKey}:`, epicError);
        return [];
      }
    }

    // Otherwise, fetch epics based on project key or all epics
    // JQL query to find all epics in the project
    const jqlQuery = projectKey
      ? `project = "${projectKey}" AND issuetype = Epic ORDER BY created DESC`
      : 'issuetype = Epic ORDER BY created DESC';

    // Search for epics using the Jira REST API
    const response = await api.asUser().requestJira(route`/rest/api/3/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        jql: jqlQuery,
        maxResults: 50,
        fields: ['summary', 'status', 'description', 'created', 'updated', 'assignee', 'customfield_10298', 'customfield_10299', 'customfield_10300', 'duedate']
      })
    });

    const data = await response.json();
    return data.issues || [];
  } catch (error) {
    console.error('Error fetching epics:', error);
    return [];
  }
}

// Function to fetch stories for an epic
async function fetchStoriesForEpic(epicKey) {
  try {
    console.log(`Fetching child issues for epic ${epicKey}`);

    // Use a more reliable JQL query to find stories that are directly linked to the epic
    const jqlQuery = `"Epic Link" = "${epicKey}" OR parent = "${epicKey}"`;
    console.log(`Using JQL query: ${jqlQuery}`);

    // Use pagination to get all results
    let startAt = 0;
    const maxResults = 100; // Max results per page
    let allIssues = [];
    let totalIssues = 1; // Initial value to enter the loop

    // Fetch all pages of results
    while (startAt < totalIssues) {
      console.log(`Fetching stories batch: startAt=${startAt}, maxResults=${maxResults}`);

      const response = await api.asUser().requestJira(route`/rest/api/3/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jql: jqlQuery,
          startAt: startAt,
          maxResults: maxResults,
          fields: ['summary', 'status', 'issuetype', 'timetracking']
        })
      });

      if (!response.ok) {
        console.error(`Error searching issues with query ${jqlQuery}: HTTP ${response.status}`);
        break;
      }

      const data = await response.json();
      totalIssues = data.total || 0;

      if (data.issues && data.issues.length > 0) {
        console.log(`Fetched batch of ${data.issues.length} stories (total: ${totalIssues})`);

        // Filter to only include stories (not subtasks or other issue types)
        const stories = data.issues.filter(issue => {
          const issueType = issue.fields && issue.fields.issuetype ?
                           issue.fields.issuetype.name : '';
          return issueType === 'Story' || issueType === 'Task';
        });

        console.log(`Filtered to ${stories.length} stories (excluding subtasks and other issue types)`);
        allIssues = allIssues.concat(stories);
      } else {
        console.log(`No stories in this batch or end of results`);
        break;
      }

      // Move to next batch
      startAt += maxResults;

      // Safety check - don't fetch more than 500 stories
      if (allIssues.length >= 500) {
        console.log(`Reached maximum story limit (500). Stopping pagination.`);
        break;
      }
    }

    console.log(`Total stories found for epic ${epicKey}: ${allIssues.length}`);

    if (allIssues.length > 0) {
      // Log the first story to see its structure
      console.log(`Sample story for ${epicKey}:`, JSON.stringify({
        key: allIssues[0].key,
        summary: allIssues[0].fields ? allIssues[0].fields.summary : allIssues[0].summary,
        status: allIssues[0].fields && allIssues[0].fields.status ?
               allIssues[0].fields.status.name :
               (allIssues[0].status ? allIssues[0].status.name : 'Unknown')
      }));
    }

    return allIssues;
  } catch (error) {
    console.error(`Error fetching stories for epic ${epicKey}:`, error);
    return [];
  }
}

// Function to fetch subtasks for a story
async function fetchSubtasksForStory(storyKey) {
  try {
    console.log(`Fetching subtasks for story ${storyKey}`);

    // Use JQL search to find subtasks - more reliable than the issue API
    const jqlQuery = `parent = "${storyKey}" AND issuetype = Subtask`;
    console.log(`Using JQL query: ${jqlQuery}`);

    // Use pagination to get all results
    let startAt = 0;
    const maxResults = 100; // Max results per page
    let allSubtasks = [];
    let totalSubtasks = 1; // Initial value to enter the loop

    // Fetch all pages of results
    while (startAt < totalSubtasks) {
      console.log(`Fetching subtasks batch: startAt=${startAt}, maxResults=${maxResults}`);

      const response = await api.asUser().requestJira(route`/rest/api/3/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jql: jqlQuery,
          startAt: startAt,
          maxResults: maxResults,
          fields: ['summary', 'status', 'issuetype', 'timetracking']
        })
      });

      if (!response.ok) {
        console.error(`Error searching subtasks with query ${jqlQuery}: HTTP ${response.status}`);
        break;
      }

      const data = await response.json();
      totalSubtasks = data.total || 0;

      if (data.issues && data.issues.length > 0) {
        console.log(`Fetched batch of ${data.issues.length} subtasks (total: ${totalSubtasks})`);
        allSubtasks = allSubtasks.concat(data.issues);
      } else {
        console.log(`No subtasks in this batch or end of results`);
        break;
      }

      // Move to next batch
      startAt += maxResults;

      // Safety check - don't fetch more than 200 subtasks per story
      if (allSubtasks.length >= 200) {
        console.log(`Reached maximum subtask limit (200) for story ${storyKey}. Stopping pagination.`);
        break;
      }
    }

    console.log(`Total subtasks found for story ${storyKey}: ${allSubtasks.length}`);

    if (allSubtasks.length > 0) {
      // Log the first subtask to see its structure
      console.log(`Sample subtask for ${storyKey}:`, JSON.stringify({
        key: allSubtasks[0].key,
        summary: allSubtasks[0].fields ? allSubtasks[0].fields.summary : allSubtasks[0].summary,
        status: allSubtasks[0].fields && allSubtasks[0].fields.status ?
               allSubtasks[0].fields.status.name :
               (allSubtasks[0].status ? allSubtasks[0].status.name : 'Unknown')
      }));
    }

    return allSubtasks;
  } catch (error) {
    console.error(`Error fetching subtasks for story ${storyKey}:`, error);
    return [];
  }
}

// Note: Progress calculation has been moved to the frontend
// This function is kept as a reference but is no longer used

// Resolver function to get saved filters
resolver.define('getSavedFilters', async () => {
  try {
    const filters = await fetchSavedFilters();
    return filters.map(filter => ({
      id: filter.id,
      name: filter.name,
      description: filter.description || '',
      jql: filter.jql
    }));
  } catch (error) {
    console.error('Error in getSavedFilters:', error);
    return [];
  }
});

// Resolver function to get epics from a filter
resolver.define('getEpicsFromFilter', async (req) => {
  try {
    console.log('getEpicsFromFilter called with context:',
      req.context && req.context.extension &&
      req.context.extension.gadgetConfiguration ?
      JSON.stringify(req.context.extension.gadgetConfiguration.filterId) : 'No filter ID in context');

    // Extract filterId from context with better error handling
    let filterId = null;

    // Try to get filterId from different possible locations in the context
    if (req.context && req.context.filterId) {
      filterId = req.context.filterId;
    } else if (req.context && req.context.extension &&
               req.context.extension.gadgetConfiguration &&
               req.context.extension.gadgetConfiguration.filterId) {
      filterId = req.context.extension.gadgetConfiguration.filterId;
    }

    console.log(`Getting epics from filter ID: ${filterId} (type: ${typeof filterId})`);

    if (!filterId) {
      console.log('No filter ID provided in context');
      return [];
    }

    // Make sure filterId is a string
    const filterIdStr = String(filterId);
    console.log(`Converted filter ID: ${filterIdStr} (type: ${typeof filterIdStr})`);

    const epics = await fetchIssuesFromFilter(filterIdStr);
    console.log(`Returning ${epics ? epics.length : 0} epics from filter ${filterIdStr}`);
    return epics;
  } catch (error) {
    console.error('Error in getEpicsFromFilter:', error);
    return [];
  }
});

// Helper function to find custom field ID by name
async function findCustomFieldId(issueKey, fieldName) {
  try {
    console.log(`\n=== Looking for custom field "${fieldName}" ===`);

    // Get field metadata to find the field by name
    const fieldsResponse = await api.asUser().requestJira(route`/rest/api/3/field`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const fieldsData = await fieldsResponse.json();
    const matchingField = fieldsData.find(field =>
      field.name && field.name === fieldName
    );

    if (matchingField) {
      console.log(`Found matching field:`);
      console.log(`  Field Name: ${matchingField.name}`);
      console.log(`  Field ID: ${matchingField.id}`);
      console.log(`  Field Key: ${matchingField.key || 'N/A'}`);
      return matchingField.id;
    } else {
      console.log(`No field found matching "${fieldName}"`);

      // Log all custom fields for debugging
      const customFields = fieldsData.filter(field => field.id.startsWith('customfield_'));
      console.log(`Available custom fields:`);
      customFields.forEach(field => {
        console.log(`  ${field.name}: ${field.id}`);
      });
    }

    return null;
  } catch (error) {
    console.error(`Error finding custom field ID: ${error.message}`);
    return null;
  }
}

// No caching implementation - always fetch fresh data

// Resolver function to get epics with their raw subtasks data for frontend calculation
resolver.define('getEpicsWithProgress', async (req) => {
  try {
    // Extract only the variables we need
    const { projectKey, epicKey, selectedEpicKey } = req.context;

    // Safely extract filterId from the context
    let filterId = null;
    if (req.context && req.context.filterId) {
      filterId = req.context.filterId;
    } else if (req.context && req.context.extension &&
        req.context.extension.gadgetConfiguration &&
        req.context.extension.gadgetConfiguration.filterId) {
      filterId = req.context.extension.gadgetConfiguration.filterId;
    }

    console.log(`getEpicsWithProgress - filterId:`, filterId);

    // No caching - always fetch fresh data

    let epics = [];

    // If filterId is provided, use it to fetch epics
    if (filterId) {
      console.log(`Using filter ID: ${filterId}`);
      epics = await fetchIssuesFromFilter(filterId);

      // If selectedEpicKey is provided, filter to only that epic
      if (selectedEpicKey) {
        console.log(`Filtering to only epic with key: ${selectedEpicKey}`);
        epics = epics.filter(epic => epic.key === selectedEpicKey);
      }
    }
    // Otherwise use the legacy approach with projectKey and epicKey
    else {
      console.log(`Using legacy approach with project/epic keys`);
      epics = await fetchEpics(projectKey, epicKey);
    }

    // Process all epics

    // Process all epics - no limit since we're doing calculations in the frontend
    // and fetching complete data for each subtask

    // Find the Latest Updates custom field ID (only do this once)
    let latestUpdatesFieldId = null;
    if (epics.length > 0) {
      console.log('Looking for Latest Updates custom field...');
      latestUpdatesFieldId = await findCustomFieldId(epics[0].key, 'Latest Updates[Short text]');
      console.log('Latest Updates field ID found:', latestUpdatesFieldId);
    }

    // Process all epics to get their stories and subtasks
    const epicsWithStoriesAndSubtasks = await Promise.all(epics.map(async (epic) => {
      const epicStatus = epic.fields.status ? epic.fields.status.name : 'Unknown';

      // Fetch all stories for this epic
      let stories = await fetchStoriesForEpic(epic.key);

      // Process each story to get its subtasks
      const storiesWithSubtasks = await Promise.all(stories.map(async (story) => {
        if (!story.key) {
          return story;
        }

        // Get the story details
        const storyStatus = story.fields && story.fields.status ?
                           story.fields.status.name :
                           (story.status ? story.status.name : 'Unknown');

        // Fetch all subtasks for this story
        const subtasks = await fetchSubtasksForStory(story.key);

        // Fetch complete data for each subtask to get time tracking information
        const subtasksWithDetails = await Promise.all(subtasks.map(async (subtask) => {
          if (!subtask.key) {
            return subtask;
          }

          try {
            // Fetch complete data for subtask to get time tracking information
            const response = await api.asUser().requestJira(route`/rest/api/3/issue/${subtask.key}`, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json'
              },
              query: {
                fields: 'summary,status,timetracking'
              }
            });

            if (response.ok) {
              const detailedSubtask = await response.json();
              return detailedSubtask;
            } else {
              return subtask;
            }
          } catch (error) {
            return subtask;
          }
        }));

        // Return the story with its subtasks
        return {
          key: story.key,
          summary: story.fields ? story.fields.summary : (story.summary || 'No summary'),
          status: storyStatus,
          subtasks: subtasksWithDetails.map(subtask => {
            // Extract time tracking information
            const timetracking = subtask.fields && subtask.fields.timetracking ? subtask.fields.timetracking : null;

            // Extract original estimate seconds
            const originalEstimateSeconds = timetracking && timetracking.originalEstimateSeconds ?
                                           timetracking.originalEstimateSeconds : null;

            // Determine the time estimate to use
            let timeEstimate = 0;
            if (originalEstimateSeconds) {
              timeEstimate = originalEstimateSeconds;
            }

            return {
              key: subtask.key,
              summary: subtask.fields ? subtask.fields.summary : (subtask.summary || 'No summary'),
              status: subtask.fields && subtask.fields.status ?
                     subtask.fields.status.name :
                     (subtask.status ? subtask.status.name : 'Unknown'),
              // Include only the time estimate information we need
              timeEstimate,
              originalEstimateSeconds
            };
          })
        };
      }));

      // Extract assignee information
      let assignee = 'Unassigned';
      if (epic.fields.assignee) {
        assignee = epic.fields.assignee.displayName || epic.fields.assignee.name || 'Unassigned';
      }

      // Extract custom fields
      let fundedBy = 'Not Specified';
      if (epic.fields.customfield_10298) {
        fundedBy = epic.fields.customfield_10298;
      }

      let salesContact = 'Not Specified';
      if (epic.fields.customfield_10299) {
        salesContact = epic.fields.customfield_10299;
      }

      let sreContact = 'Not Specified';
      if (epic.fields.customfield_10300) {
        sreContact = epic.fields.customfield_10300;
      }

      let dueDate = null;
      if (epic.fields.duedate) {
        dueDate = epic.fields.duedate;
      }

      // Extract Latest Updates field
      let latestUpdates = 'Not Specified';
      if (latestUpdatesFieldId && epic.fields[latestUpdatesFieldId]) {
        latestUpdates = epic.fields[latestUpdatesFieldId];
        console.log(`Latest Updates for ${epic.key}: ${latestUpdates}`);
      }

      // Return the epic with its stories and subtasks and custom fields
      return {
        key: epic.key,
        summary: epic.fields.summary,
        status: epicStatus,
        assignee: assignee,
        fundedBy: fundedBy,
        salesContact: salesContact,
        sreContact: sreContact,
        dueDate: dueDate,
        latestUpdates: latestUpdates,
        stories: storiesWithSubtasks
      };
    }));

    // Return all epics with their stories and subtasks without caching
    return epicsWithStoriesAndSubtasks;
  } catch (error) {
    console.error('Error in getEpicsWithProgress:', error);
    return [];
  }
});

export const handler = resolver.getDefinitions();
