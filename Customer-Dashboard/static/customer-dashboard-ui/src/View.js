import { useEffect, useState, useMemo } from 'react';
import { view, invoke, router } from '@forge/bridge';
import './View.css';

// Utility function to add classic class if needed
const addClassicClass = (baseClass, isClassic) =>
  `${baseClass}${isClassic ? ' classic' : ''}`;

// Sortable Header Component
const SortableHeader = ({
  label,
  sortKey,
  currentSortConfig,
  onSort,
  className = '',
  centerContent = false
}) => {
  const isSorted = currentSortConfig.key === sortKey;
  const sortDirection = isSorted ? currentSortConfig.direction : null;

  return (
    <th onClick={() => onSort(sortKey)} className={`sortable-header ${isSorted ? 'sorted' : ''} ${className}`}>
      <div className={`cell-content ${centerContent ? 'cell-content-center' : ''}`}>
        <div className="header-content">
          <span>{label}</span>
          <span className="sort-indicator">
            {isSorted && (sortDirection === 'asc' ? '▲' : '▼')}
          </span>
        </div>
      </div>
    </th>
  );
};

// Progress Bar Component
const ProgressBar = ({ percentage, showLabel = false, highlightColor, uiStyle = 'modern', daysDisplay }) => {
  const isClassic = uiStyle === 'classic';

  // Determine the class based on the percentage
  const progressClass = useMemo(() => {
    let baseClass = "progress-bar-fill";
    if (percentage === 100) {
      baseClass += " complete";
    } else if (percentage > 0) {
      baseClass += " in-progress";
    } else {
      baseClass += " not-started";
    }
    return addClassicClass(baseClass, isClassic);
  }, [percentage, isClassic]);

  // Determine the color based on the percentage and highlight color
  const barColor = useMemo(() => {
    if (isClassic) {
      if (percentage === 100) return '#14892c'; // Jira green
      if (percentage > 0) return highlightColor || '#4a6785'; // Use highlight color or default Jira blue-gray
      return '#707070'; // Jira gray
    } else {
      if (percentage === 100) return '#36B37E'; // Always use green for completed
      if (percentage === 0) return '#6B778C'; // Always use gray for not started
      return highlightColor || '#0052CC'; // Default to blue
    }
  }, [percentage, highlightColor, isClassic]);

  return (
    <div className={addClassicClass('progress-wrapper', isClassic)}>
      <div className={addClassicClass('progress-bar-container', isClassic)}>
        <div
          className={progressClass}
          style={{
            width: `${percentage}%`,
            backgroundColor: barColor
          }}
          aria-valuenow={percentage}
          aria-valuemin="0"
          aria-valuemax="100"
        />
      </div>
      {showLabel && (
        <div
          className={addClassicClass('progress-percentage', isClassic)}
          style={{ color: !isClassic && percentage > 0 ? barColor : '#172B4D' }}
        >
          {percentage}%{daysDisplay ? <span className="days-display"> ({daysDisplay})</span> : ''}
        </div>
      )}
    </div>
  );
};

// Overall Progress Component
const OverallProgress = ({ epics, showOverall, highlightColor, uiStyle = 'modern' }) => {
  if (!showOverall || !epics || epics.length === 0) {
    return null;
  }

  const isClassic = uiStyle === 'classic';

  // Calculate overall progress
  const totalEpics = epics.length;
  const completedEpics = epics.filter(epic => epic.progressPercentage === 100).length;

  // Calculate weighted progress based on all epics
  const overallPercentage = useMemo(() => {
    // Check if epics have progressPercentage property
    if (epics[0] && typeof epics[0].progressPercentage === 'number') {
      const totalProgress = epics.reduce((sum, epic) => sum + epic.progressPercentage, 0);
      return Math.round(totalProgress / totalEpics);
    }
    return 0;
  }, [epics]);

  return (
    <div
      className={addClassicClass('overall-progress-section', isClassic)}
      style={{ borderLeftColor: !isClassic ? (highlightColor || '#0052CC') : '#DFE1E6' }}
    >
      <div className={addClassicClass('overall-progress-title', isClassic)}>Overall Progress</div>
      <div className={addClassicClass('overall-progress-stats', isClassic)}>
        <div
          className={addClassicClass('overall-progress-percentage', isClassic)}
          style={{ color: !isClassic ? (highlightColor || '#0052CC') : '#172B4D' }}
        >
          {overallPercentage}%
        </div>
        <div className={addClassicClass('overall-progress-details', isClassic)}>
          {completedEpics} of {totalEpics} customers completed
        </div>
      </div>
      <ProgressBar percentage={overallPercentage} highlightColor={highlightColor} uiStyle={uiStyle} />
    </div>
  );
};

// Epic Row Component
const EpicRow = ({
  epic,
  highlightColor,
  uiStyle = 'modern',
  showFundedByColumn,
  showSalesContactColumn,
  showCsmColumn,
  showSreContactColumn,
  showDueDateColumn,
  showTasksColumns
}) => {
  const isClassic = uiStyle === 'classic';

  // Determine status class and text
  const { statusClass, statusText } = useMemo(() => {
    let baseClass = "epic-status";
    let text = epic.status;

    if (epic.progressPercentage === 100) {
      baseClass += " done";
      text = "Done";
    } else if (epic.progressPercentage > 0) {
      baseClass += " in-progress";
      text = "In Progress";
    }

    return {
      statusClass: addClassicClass(baseClass, isClassic),
      statusText: text
    };
  }, [epic.progressPercentage, epic.status, isClassic]);

  // Handle epic link click
  const handleEpicClick = (e) => {
    e.preventDefault();
    router.open(`/browse/${epic.key}`, '_blank');
  };

  // Format due date if available
  const formattedDueDate = epic.dueDate ? new Date(epic.dueDate).toLocaleDateString() : 'Not set';

  return (
    <tr className={isClassic ? 'classic-row' : ''}>
      <td>
        <div className="cell-content">
          <a
            href="#"
            onClick={handleEpicClick}
            className={addClassicClass('epic-name', isClassic)}
            style={{ color: highlightColor || '#0052CC' }}
            title={`${epic.key}: ${epic.summary}`}
          >
            {epic.summary}
          </a>
        </div>
      </td>
      {showFundedByColumn && (
        <td>
          <div className="cell-content cell-content-center">
            <span className={addClassicClass('field-value', isClassic)}>
              {epic.fundedBy || 'Not specified'}
            </span>
          </div>
        </td>
      )}
      {showSalesContactColumn && (
        <td>
          <div className="cell-content cell-content-center">
            <span className={addClassicClass('field-value', isClassic)}>
              {epic.salesContact || 'Not specified'}
            </span>
          </div>
        </td>
      )}
      {showCsmColumn && (
        <td>
          <div className="cell-content cell-content-center assignee-cell">
            <span
              className={addClassicClass('assignee-name', isClassic)}
              title={epic.assignee}
            >
              {epic.assignee}
            </span>
          </div>
        </td>
      )}
      {showSreContactColumn && (
        <td>
          <div className="cell-content cell-content-center">
            <span className={addClassicClass('field-value', isClassic)}>
              {epic.sreContact || 'Not specified'}
            </span>
          </div>
        </td>
      )}
      {showDueDateColumn && (
        <td>
          <div className="cell-content cell-content-center">
            <span className={addClassicClass('field-value', isClassic)}>
              {formattedDueDate}
            </span>
          </div>
        </td>
      )}
      {showTasksColumns && (
        <>
          <td>
            <div className="cell-content cell-content-center">
              <span className={addClassicClass('count-badge', isClassic)}>
                {epic.totalSubtasks}
              </span>
            </div>
          </td>
          <td>
            <div className="cell-content cell-content-center">
              <span className={`count-badge ${epic.completedSubtasks > 0 ? "completed" : ""} ${isClassic ? 'classic' : ''}`}>
                {epic.completedSubtasks}
              </span>
            </div>
          </td>
        </>
      )}
      <td>
        <div className="cell-content">
          <ProgressBar
            percentage={epic.progressPercentage}
            showLabel={true}
            highlightColor={highlightColor}
            uiStyle={uiStyle}
            daysDisplay={epic.daysDisplay}
          />
        </div>
      </td>
    </tr>
  );
};

function View() {
  const [context, setContext] = useState();
  const [epics, setEpics] = useState([]); // Epics with progress data
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Add state for sorting
  const [sortConfig, setSortConfig] = useState({
    key: 'progressPercentage', // Default sort by progress
    direction: 'desc'          // Default sort direction (descending)
  });

  // Get context and fetch epics when component mounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get context for configuration
        const contextData = await view.getContext();
        setContext(contextData);

        // Get configuration options
        const gadgetConfig = contextData?.extension?.gadgetConfiguration || {};
        const config = {
          showOverall: gadgetConfig.showOverall !== false,
          highlightColor: gadgetConfig.highlightColor || '#0052CC',
          uiStyle: gadgetConfig.uiStyle || 'modern',
          filterId: gadgetConfig.filterId,
          csmColumnLabel: gadgetConfig.csmColumnLabel || 'CSM Assigned',
          showCsmColumn: gadgetConfig.showCsmColumn !== false,
          fundedByColumnLabel: gadgetConfig.fundedByColumnLabel || 'Funded By',
          showFundedByColumn: gadgetConfig.showFundedByColumn !== false,
          salesContactColumnLabel: gadgetConfig.salesContactColumnLabel || 'Sales Contact',
          showSalesContactColumn: gadgetConfig.showSalesContactColumn !== false,
          sreContactColumnLabel: gadgetConfig.sreContactColumnLabel || 'SRE Contact',
          showSreContactColumn: gadgetConfig.showSreContactColumn !== false,
          dueDateColumnLabel: gadgetConfig.dueDateColumnLabel || 'Due Date',
          showDueDateColumn: gadgetConfig.showDueDateColumn !== false,
          showTasksColumns: gadgetConfig.showTasksColumns !== false
        };

        const filterId = config.filterId;
        const showOverall = config.showOverall;
        const csmColumnLabel = config.csmColumnLabel;
        const fundedByColumnLabel = config.fundedByColumnLabel;
        const salesContactColumnLabel = config.salesContactColumnLabel;
        const sreContactColumnLabel = config.sreContactColumnLabel;
        const dueDateColumnLabel = config.dueDateColumnLabel;
        const highlightColor = config.highlightColor;
        const uiStyle = config.uiStyle;

        if (!filterId) {
          setLoading(false);
          return;
        }

        // Fetch epics with progress data with all configuration options
        const epicsData = await invoke('getEpicsWithProgress', {
          context: {
            filterId: filterId,
            showOverall,
            csmColumnLabel,
            fundedByColumnLabel,
            salesContactColumnLabel,
            sreContactColumnLabel,
            dueDateColumnLabel,
            highlightColor,
            uiStyle,
            showCsmColumn: config.showCsmColumn !== false,
            showFundedByColumn: config.showFundedByColumn !== false,
            showSalesContactColumn: config.showSalesContactColumn !== false,
            showSreContactColumn: config.showSreContactColumn !== false,
            showDueDateColumn: config.showDueDateColumn !== false,
            showTasksColumns: config.showTasksColumns !== false
          }
        });



        setEpics(epicsData);
      } catch (err) {
        console.error('Error fetching data:', err);
        // Provide more detailed error message if available
        const errorMessage = err.message
          ? `Error: ${err.message}`
          : 'Failed to load epic data. Please try again later.';

        setError(errorMessage);

        // Log additional details for debugging
        if (err.stack) {
          console.error('Error stack:', err.stack);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Get configuration options (with defaults)
  const config = useMemo(() => {
    const gadgetConfig = context?.extension?.gadgetConfiguration || {};
    return {
      showOverall: gadgetConfig.showOverall !== false,
      highlightColor: gadgetConfig.highlightColor || '#0052CC',
      uiStyle: gadgetConfig.uiStyle || 'modern',
      filterId: gadgetConfig.filterId,
      csmColumnLabel: gadgetConfig.csmColumnLabel || 'CSM Assigned',
      showCsmColumn: gadgetConfig.showCsmColumn !== false,
      fundedByColumnLabel: gadgetConfig.fundedByColumnLabel || 'Funded By',
      showFundedByColumn: gadgetConfig.showFundedByColumn !== false,
      salesContactColumnLabel: gadgetConfig.salesContactColumnLabel || 'Sales Contact',
      showSalesContactColumn: gadgetConfig.showSalesContactColumn !== false,
      sreContactColumnLabel: gadgetConfig.sreContactColumnLabel || 'SRE Contact',
      showSreContactColumn: gadgetConfig.showSreContactColumn !== false,
      dueDateColumnLabel: gadgetConfig.dueDateColumnLabel || 'Due Date',
      showDueDateColumn: gadgetConfig.showDueDateColumn !== false,
      // Always show tasks columns for now until we fix the backend issue
      showTasksColumns: true
    };
  }, [context]);

  const {
    showOverall,
    highlightColor,
    uiStyle,
    filterId,
    csmColumnLabel,
    showCsmColumn,
    fundedByColumnLabel,
    showFundedByColumn,
    salesContactColumnLabel,
    showSalesContactColumn,
    sreContactColumnLabel,
    showSreContactColumn,
    dueDateColumnLabel,
    showDueDateColumn,
    showTasksColumns
  } = config;

  const isClassic = uiStyle === 'classic';

  // Function to handle column header clicks for sorting
  const handleSort = (key) => {
    setSortConfig(prevConfig => {
      // If clicking the same column, toggle direction
      if (prevConfig.key === key) {
        return {
          key,
          direction: prevConfig.direction === 'asc' ? 'desc' : 'asc'
        };
      }
      // If clicking a different column, sort by that column in descending order by default
      // (except for customer name which defaults to ascending)
      return {
        key,
        direction: key === 'summary' ? 'asc' : 'desc'
      };
    });
  };

  // Calculate progress for each epic
  const processedEpics = useMemo(() => {
    if (!epics || epics.length === 0) return [];

    return epics.map(epic => {
      // Get stories for this epic
      const stories = epic.stories || [];

      // Initialize counters
      let totalSubtasks = 0;
      let completedSubtasks = 0;
      let totalEstimate = 0;
      let completedEstimate = 0;
      let issuesWithEstimates = 0;

      // Process each story and its subtasks
      stories.forEach(story => {
        // Get subtasks for this story
        const subtasks = story.subtasks || [];

        // Add to total subtasks count
        totalSubtasks += subtasks.length;

        // Count completed subtasks
        const storyCompletedSubtasks = subtasks.filter(subtask => {
          const status = subtask.status.toLowerCase();
          return status.includes('done') ||
                 status.includes('resolved') ||
                 status.includes('closed');
        }).length;

        // Add to completed subtasks count
        completedSubtasks += storyCompletedSubtasks;

        // Process each subtask to get time estimates
        subtasks.forEach(subtask => {
          // Use the best available time estimate
          let timeEstimate = 0;
          let hasEstimate = false;

          try {
            // Use originalEstimateSeconds or fall back to timeEstimate
            if (subtask && subtask.originalEstimateSeconds) {
              timeEstimate = subtask.originalEstimateSeconds;
              hasEstimate = true;
            } else if (subtask && subtask.timeEstimate) {
              timeEstimate = subtask.timeEstimate;
              hasEstimate = true;
            }
            // Don't use default estimates anymore
          } catch (error) {
            // Don't use default estimates on error
            console.error('Error processing subtask time estimate:', error);
          }

          // Only add to total if the subtask has an explicit estimate
          if (hasEstimate) {
            totalEstimate += timeEstimate;
            if (timeEstimate > 0) {
              issuesWithEstimates++;
            }
          }

          // Check if the subtask is completed
          let isCompleted = false;
          try {
            const status = subtask && subtask.status ? subtask.status.toLowerCase() : '';
            isCompleted = status.includes('done') ||
                         status.includes('resolved') ||
                         status.includes('closed');
          } catch (error) {
            isCompleted = false;
          }

          // If the subtask is completed and has an estimate, add its time estimate to the completed estimate
          if (isCompleted && hasEstimate) {
            completedEstimate += timeEstimate;
          }
        });
      });

      // Calculate progress percentage - always use time-based calculation
      let progressPercentage = 0;
      let progressCalculationMethod = '';

      // Always use time-based calculation when there are subtasks
      if (totalSubtasks > 0) {
        if (totalEstimate > 0) {
          // If we have time estimates, use them
          progressPercentage = (completedEstimate / totalEstimate) * 100;
          progressCalculationMethod = 'time-based';
        } else if (issuesWithEstimates === 0) {
          // If no subtasks have estimates, fall back to count-based
          progressPercentage = (completedSubtasks / totalSubtasks) * 100;
          progressCalculationMethod = 'count-based';
        } else {
          // If some subtasks have estimates but total is 0, set progress to 0
          progressPercentage = 0;
          progressCalculationMethod = 'no-valid-estimates';
        }
      }

      // Check if the epic itself is complete based on its status
      const epicStatusComplete = epic.status.toLowerCase().includes('done') ||
                               epic.status.toLowerCase().includes('resolved') ||
                               epic.status.toLowerCase().includes('closed');

      // Check if all subtasks are complete
      const subtasksComplete = totalSubtasks > 0 ? completedSubtasks === totalSubtasks : false;

      // Determine overall completion status
      const isComplete = epicStatusComplete && subtasksComplete;

      // Convert seconds to days for display
      const totalDays = totalEstimate > 0 ? (totalEstimate / (8 * 60 * 60)).toFixed(1) : 0; // 8 hours per day
      const completedDays = completedEstimate > 0 ? (completedEstimate / (8 * 60 * 60)).toFixed(1) : 0;

      // Create the days display string (e.g., "2.5/5.0 days")
      let daysDisplay;
      if (issuesWithEstimates === 0) {
        // No subtasks have estimates
        daysDisplay = 'No time estimates';
      } else if (totalEstimate === 0) {
        // Some subtasks have estimates but total is 0
        daysDisplay = 'Invalid estimates';
      } else {
        // Normal case with valid estimates
        daysDisplay = `${completedDays}/${totalDays} days`;
      }


      const processedEpic = {
        ...epic,
        totalSubtasks,
        completedSubtasks,
        progressPercentage: Math.round(progressPercentage),
        daysDisplay,
        totalDays,
        completedDays,
        isComplete,
        epicStatusComplete,
        subtasksComplete,
        totalEstimate,
        completedEstimate,
        issuesWithEstimates,
        progressCalculationMethod
      };

      return processedEpic;
    });
  }, [epics]);

  // Sort epics based on the current sort configuration
  const sortedEpics = useMemo(() => {
    if (!processedEpics.length) return [];

    const sortableEpics = [...processedEpics];

    return sortableEpics.sort((a, b) => {
      // Get the values to compare based on the sort key
      let aValue, bValue;

      switch (sortConfig.key) {
        case 'summary':
          aValue = a.summary || '';
          bValue = b.summary || '';
          // Case-insensitive string comparison
          return sortConfig.direction === 'asc'
            ? aValue.localeCompare(bValue, undefined, { sensitivity: 'base' })
            : bValue.localeCompare(aValue, undefined, { sensitivity: 'base' });

        case 'fundedBy':
          aValue = a.fundedBy || '';
          bValue = b.fundedBy || '';
          return sortConfig.direction === 'asc'
            ? aValue.localeCompare(bValue, undefined, { sensitivity: 'base' })
            : bValue.localeCompare(aValue, undefined, { sensitivity: 'base' });

        case 'salesContact':
          aValue = a.salesContact || '';
          bValue = b.salesContact || '';
          return sortConfig.direction === 'asc'
            ? aValue.localeCompare(bValue, undefined, { sensitivity: 'base' })
            : bValue.localeCompare(aValue, undefined, { sensitivity: 'base' });

        case 'assignee':
          aValue = a.assignee || '';
          bValue = b.assignee || '';
          return sortConfig.direction === 'asc'
            ? aValue.localeCompare(bValue, undefined, { sensitivity: 'base' })
            : bValue.localeCompare(aValue, undefined, { sensitivity: 'base' });

        case 'sreContact':
          aValue = a.sreContact || '';
          bValue = b.sreContact || '';
          return sortConfig.direction === 'asc'
            ? aValue.localeCompare(bValue, undefined, { sensitivity: 'base' })
            : bValue.localeCompare(aValue, undefined, { sensitivity: 'base' });

        case 'dueDate':
          // Convert to dates for comparison, defaulting to far future for empty dates
          aValue = a.dueDate ? new Date(a.dueDate) : new Date(8640000000000000);
          bValue = b.dueDate ? new Date(b.dueDate) : new Date(8640000000000000);
          return sortConfig.direction === 'asc'
            ? aValue - bValue
            : bValue - aValue;

        case 'totalSubtasks':
          aValue = a.totalSubtasks || 0;
          bValue = b.totalSubtasks || 0;
          return sortConfig.direction === 'asc'
            ? aValue - bValue
            : bValue - aValue;

        case 'completedSubtasks':
          aValue = a.completedSubtasks || 0;
          bValue = b.completedSubtasks || 0;
          return sortConfig.direction === 'asc'
            ? aValue - bValue
            : bValue - aValue;

        case 'progressPercentage':
        default:
          aValue = a.progressPercentage || 0;
          bValue = b.progressPercentage || 0;
          return sortConfig.direction === 'asc'
            ? aValue - bValue
            : bValue - aValue;
      }
    });
  }, [processedEpics, sortConfig]);

  // Common container component for different states
  const Container = ({ children }) => (
    <div
      className={addClassicClass('epic-progress-tracker', isClassic)}
      style={{ '--highlight-color': highlightColor }}
    >
      <h2>Customer Progress</h2>
      {children}
    </div>
  );

  // Loading state
  if (loading) {
    return (
      <Container>
        <div className="loading">
          <div className="loading-icon"></div>
          Loading customer data...
        </div>
      </Container>
    );
  }

  // Error state
  if (error) {
    return (
      <Container>
        <div className="error">
          <h3>Error Loading Data</h3>
          <p>{error}</p>
          <p>
            Try refreshing the page or checking your filter configuration.
            If the problem persists, contact your administrator.
          </p>
        </div>
      </Container>
    );
  }

  // No filter selected
  if (!filterId) {
    return (
      <Container>
        <div className="no-data">
          No filter selected. Please edit the gadget configuration to select a saved filter.
        </div>
      </Container>
    );
  }

  // No epics found
  if (epics.length === 0) {
    return (
      <Container>
        <div className="no-data">
          No customers found in the selected filter. Try selecting a different filter or check your Jira permissions.
        </div>
      </Container>
    );
  }

  // Main view with data
  return (
    <Container>
      {/* Overall Progress Section */}
      <OverallProgress
        epics={sortedEpics}
        showOverall={showOverall}
        highlightColor={highlightColor}
        uiStyle={uiStyle}
      />

      {/* Calculation info message - explicitly disabled */}
      {false && (
        <div className="calculation-info">
          <span className="info-icon">ℹ️</span>
          <span>
            Progress calculations performed in your browser for maximum accuracy.
            {epics.reduce((total, epic) => {
              let count = 0;
              (epic.stories || []).forEach(story => {
                count += (story.subtasks || []).length;
              });
              return total + count;
            }, 0)} total tasks processed.
          </span>
        </div>
      )}

      <div className="epic-table-container">
        <table className="epic-table">
          <thead>
            <tr className="table-header-row">
              <SortableHeader
                label="Customer Name"
                sortKey="summary"
                currentSortConfig={sortConfig}
                onSort={handleSort}
              />

              {showFundedByColumn && (
                <SortableHeader
                  label={fundedByColumnLabel}
                  sortKey="fundedBy"
                  currentSortConfig={sortConfig}
                  onSort={handleSort}
                  centerContent={true}
                />
              )}

              {showSalesContactColumn && (
                <SortableHeader
                  label={salesContactColumnLabel}
                  sortKey="salesContact"
                  currentSortConfig={sortConfig}
                  onSort={handleSort}
                  centerContent={true}
                />
              )}

              {showCsmColumn && (
                <SortableHeader
                  label={csmColumnLabel}
                  sortKey="assignee"
                  currentSortConfig={sortConfig}
                  onSort={handleSort}
                  centerContent={true}
                />
              )}

              {showSreContactColumn && (
                <SortableHeader
                  label={sreContactColumnLabel}
                  sortKey="sreContact"
                  currentSortConfig={sortConfig}
                  onSort={handleSort}
                  centerContent={true}
                />
              )}

              {showDueDateColumn && (
                <SortableHeader
                  label={dueDateColumnLabel}
                  sortKey="dueDate"
                  currentSortConfig={sortConfig}
                  onSort={handleSort}
                  centerContent={true}
                />
              )}

              {showTasksColumns && (
                <>
                  <SortableHeader
                    label="All Tasks"
                    sortKey="totalSubtasks"
                    currentSortConfig={sortConfig}
                    onSort={handleSort}
                    centerContent={true}
                  />
                  <SortableHeader
                    label="Completed Tasks"
                    sortKey="completedSubtasks"
                    currentSortConfig={sortConfig}
                    onSort={handleSort}
                    centerContent={true}
                  />
                </>
              )}

              <SortableHeader
                label="Progress"
                sortKey="progressPercentage"
                currentSortConfig={sortConfig}
                onSort={handleSort}
              />
            </tr>
          </thead>
          <tbody>
            {sortedEpics.map(epic => (
              <EpicRow
                key={epic.key}
                epic={epic}
                highlightColor={highlightColor}
                uiStyle={uiStyle}
                showFundedByColumn={showFundedByColumn}
                showSalesContactColumn={showSalesContactColumn}
                showCsmColumn={showCsmColumn}
                showSreContactColumn={showSreContactColumn}
                showDueDateColumn={showDueDateColumn}
                showTasksColumns={showTasksColumns}
              />
            ))}
          </tbody>
        </table>
      </div>
    </Container>
  );
}

export default View;
