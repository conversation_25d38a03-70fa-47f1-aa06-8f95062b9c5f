import React, { useEffect, useState, useCallback, useMemo } from 'react';
import Form, { Field } from '@atlaskit/form';
import TextField from '@atlaskit/textfield';
import Button, { ButtonGroup } from '@atlaskit/button';
import Select from '@atlaskit/select';
import { invoke, view } from '@forge/bridge';
import './Edit.css';

function Edit() {
  const [config, setConfig] = useState({});
  const [selectedColor, setSelectedColor] = useState('#0052CC'); // Default blue color
  const [savedFilters, setSavedFilters] = useState([]);
  const [filterEpics, setFilterEpics] = useState([]);
  const [loading, setLoading] = useState(true);
  const [epicsLoading, setEpicsLoading] = useState(false);

  // Fetch epics when a filter is selected - optimized with useCallback
  const fetchEpicsForFilter = useCallback(async (filterId) => {
    if (!filterId) {
      setFilterEpics([]);
      return;
    }

    try {
      setEpicsLoading(true);

      const epics = await invoke('getEpicsFromFilter', {
        context: { filterId }
      });

      if (!epics || epics.length === 0) {
        setFilterEpics([]);
        return;
      }

      // Transform epics for the select component with error handling
      const epicOptions = epics
        .filter(epic => epic && epic.fields && epic.fields.summary && epic.key)
        .map(epic => {
          try {
            return {
              label: epic.fields.summary,
              value: epic.key,
              key: epic.key
            };
          } catch (err) {
            console.error(`Error processing epic ${epic.key || 'unknown'}:`, err);
            return null;
          }
        })
        .filter(option => option !== null);

      setFilterEpics(epicOptions);
    } catch (error) {
      console.error('Error fetching epics:', error);
      setFilterEpics([]);
    } finally {
      setEpicsLoading(false);
    }
  }, []);

  // Initialize component data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Get context for current configuration
        const context = await view.getContext();
        const currentConfig = context?.extension?.gadgetConfiguration || {};
        setConfig(currentConfig);

        // Set the selected color from the configuration or use default
        if (currentConfig.highlightColor) {
          setSelectedColor(currentConfig.highlightColor);
        }

        // Fetch saved filters
        const filters = await invoke('getSavedFilters');

        // Transform filters for the select component
        const filterOptions = filters.map(filter => ({
          label: filter.name,
          value: filter.id,
          description: filter.description,
          jql: filter.jql
        }));

        setSavedFilters(filterOptions);

        // If a filter is already selected, fetch its epics
        if (currentConfig.filterId) {
          await fetchEpicsForFilter(currentConfig.filterId);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [fetchEpicsForFilter]);

  // Handle form submission
  const onSubmit = useCallback((formData) => {
    view.submit(formData);
  }, []);

  // Set CSS variable for highlight color
  const containerStyle = useMemo(() => ({
    '--highlight-color': selectedColor
  }), [selectedColor]);

  return (
    <div className="edit-container" style={containerStyle}>
      <h2>Epic Progress Tracker Configuration</h2>
      <p className="description">
        Configure the Epic Progress Tracker dashboard gadget. Customize the appearance and behavior
        to match your team's preferences.
      </p>

      <Form onSubmit={onSubmit}>
        {({ formProps, submitting }) => (
          <form {...formProps}>
            <Field
              name="filterId"
              label="Saved Filter"
              defaultValue={config.filterId || ''}
              description="Select a saved filter to show epics from that filter"
            >
              {({ fieldProps }) => (
                <div>
                  {loading ? (
                    <div className="loading-indicator">Loading filters...</div>
                  ) : (
                    <Select
                      inputId={fieldProps.id}
                      className="filter-select"
                      classNamePrefix="filter"
                      options={savedFilters}
                      placeholder="Select a saved filter"
                      isSearchable
                      isClearable
                      value={savedFilters.find(f => f.value === fieldProps.value) || null}
                      onChange={(option) => {
                        const value = option ? option.value : '';
                        fieldProps.onChange(value);
                        fetchEpicsForFilter(value);
                      }}
                      menuPortalTarget={document.body}
                      styles={{ menuPortal: base => ({ ...base, zIndex: 9999 }) }}
                    />
                  )}
                  <div className="field-description">
                    Select a saved filter that contains epics. Only epics from this filter will be displayed.
                  </div>
                </div>
              )}
            </Field>



            <Field
              name="showOverall"
              label="Show Overall Progress"
              defaultValue={config.showOverall !== false} // Default to true if not set
              description="Display overall progress percentage across all epics"
            >
              {({ fieldProps }) => (
                <div className="checkbox-container">
                  <input
                    type="checkbox"
                    id="showOverall"
                    checked={fieldProps.value}
                    onChange={(e) => fieldProps.onChange(e.target.checked)}
                  />
                  <label htmlFor="showOverall" className="checkbox-label">
                    Show overall progress section
                  </label>
                </div>
              )}
            </Field>

            <h3 className="section-header">Column Visibility and Labels</h3>

            <div className="columns-section">
              <Field
                name="showFundedByColumn"
                label="Show Funded By Column"
                defaultValue={config.showFundedByColumn !== false}
              >
                {({ fieldProps }) => (
                  <div className="checkbox-container">
                    <input
                      type="checkbox"
                      id="showFundedByColumn"
                      checked={fieldProps.value}
                      onChange={(e) => fieldProps.onChange(e.target.checked)}
                    />
                    <label htmlFor="showFundedByColumn" className="checkbox-label">
                      Show Funded By Column
                    </label>
                  </div>
                )}
              </Field>

              <Field
                name="fundedByColumnLabel"
                label="Funded By Column Label"
                defaultValue={config.fundedByColumnLabel || 'Funded By'}
                description="Customize the label for the Funded By column"
              >
                {({ fieldProps }) => (
                  <div>
                    <TextField
                      {...fieldProps}
                      placeholder="Enter column label"
                    />
                    <div className="field-description">
                      Change the label of the Funded By column to match your organization's terminology
                    </div>
                  </div>
                )}
              </Field>
            </div>

            <div className="columns-section">
              <Field
                name="showSalesContactColumn"
                label="Show Sales Contact Column"
                defaultValue={config.showSalesContactColumn !== false}
              >
                {({ fieldProps }) => (
                  <div className="checkbox-container">
                    <input
                      type="checkbox"
                      id="showSalesContactColumn"
                      checked={fieldProps.value}
                      onChange={(e) => fieldProps.onChange(e.target.checked)}
                    />
                    <label htmlFor="showSalesContactColumn" className="checkbox-label">
                      Show Sales Contact Column
                    </label>
                  </div>
                )}
              </Field>

              <Field
                name="salesContactColumnLabel"
                label="Sales Contact Column Label"
                defaultValue={config.salesContactColumnLabel || 'Sales Contact'}
                description="Customize the label for the Sales Contact column"
              >
                {({ fieldProps }) => (
                  <div>
                    <TextField
                      {...fieldProps}
                      placeholder="Enter column label"
                    />
                    <div className="field-description">
                      Change the label of the Sales Contact column to match your organization's terminology
                    </div>
                  </div>
                )}
              </Field>
            </div>

            <div className="columns-section">
              <Field
                name="showCsmColumn"
                label="Show CSM Column"
                defaultValue={config.showCsmColumn !== false}
              >
                {({ fieldProps }) => (
                  <div className="checkbox-container">
                    <input
                      type="checkbox"
                      id="showCsmColumn"
                      checked={fieldProps.value}
                      onChange={(e) => fieldProps.onChange(e.target.checked)}
                    />
                    <label htmlFor="showCsmColumn" className="checkbox-label">
                      Show CSM Column
                    </label>
                  </div>
                )}
              </Field>

              <Field
                name="csmColumnLabel"
                label="CSM Column Label"
                defaultValue={config.csmColumnLabel || 'CSM Assigned'}
                description="Customize the label for the CSM Assigned column"
              >
                {({ fieldProps }) => (
                  <div>
                    <TextField
                      {...fieldProps}
                      placeholder="Enter column label"
                    />
                    <div className="field-description">
                      Change the label of the CSM column to match your organization's terminology
                    </div>
                  </div>
                )}
              </Field>
            </div>

            <div className="columns-section">
              <Field
                name="showSreContactColumn"
                label="Show SRE Contact Column"
                defaultValue={config.showSreContactColumn !== false}
              >
                {({ fieldProps }) => (
                  <div className="checkbox-container">
                    <input
                      type="checkbox"
                      id="showSreContactColumn"
                      checked={fieldProps.value}
                      onChange={(e) => fieldProps.onChange(e.target.checked)}
                    />
                    <label htmlFor="showSreContactColumn" className="checkbox-label">
                      Show SRE Contact Column
                    </label>
                  </div>
                )}
              </Field>

              <Field
                name="sreContactColumnLabel"
                label="SRE Contact Column Label"
                defaultValue={config.sreContactColumnLabel || 'SRE Contact'}
                description="Customize the label for the SRE Contact column"
              >
                {({ fieldProps }) => (
                  <div>
                    <TextField
                      {...fieldProps}
                      placeholder="Enter column label"
                    />
                    <div className="field-description">
                      Change the label of the SRE Contact column to match your organization's terminology
                    </div>
                  </div>
                )}
              </Field>
            </div>

            <div className="columns-section">
              <Field
                name="showDueDateColumn"
                label="Show Due Date Column"
                defaultValue={config.showDueDateColumn !== false}
              >
                {({ fieldProps }) => (
                  <div className="checkbox-container">
                    <input
                      type="checkbox"
                      id="showDueDateColumn"
                      checked={fieldProps.value}
                      onChange={(e) => fieldProps.onChange(e.target.checked)}
                    />
                    <label htmlFor="showDueDateColumn" className="checkbox-label">
                      Show Due Date Column
                    </label>
                  </div>
                )}
              </Field>

              <Field
                name="dueDateColumnLabel"
                label="Due Date Column Label"
                defaultValue={config.dueDateColumnLabel || 'Due Date'}
                description="Customize the label for the Due Date column"
              >
                {({ fieldProps }) => (
                  <div>
                    <TextField
                      {...fieldProps}
                      placeholder="Enter column label"
                    />
                    <div className="field-description">
                      Change the label of the Due Date column to match your organization's terminology
                    </div>
                  </div>
                )}
              </Field>
            </div>

            <Field
              name="showTasksColumns"
              label="Show Tasks Columns"
              defaultValue={config.showTasksColumns !== false}
              description="Show or hide the All Tasks and Completed Tasks columns"
            >
              {({ fieldProps }) => (
                <div className="checkbox-container">
                  <input
                    type="checkbox"
                    id="showTasksColumns"
                    checked={fieldProps.value}
                    onChange={(e) => fieldProps.onChange(e.target.checked)}
                  />
                  <label htmlFor="showTasksColumns" className="checkbox-label">
                    Show Tasks Columns (All Tasks and Completed Tasks)
                  </label>
                </div>
              )}
            </Field>

            <div className="form-actions">
              <ButtonGroup>
                <Button
                  type="submit"
                  appearance="primary"
                  isDisabled={submitting}
                  style={{
                    backgroundColor: selectedColor,
                    borderColor: selectedColor
                  }}
                >
                  Save Configuration
                </Button>
                <Button appearance="subtle" onClick={view.close}>
                  Cancel
                </Button>
              </ButtonGroup>
            </div>
          </form>
        )}
      </Form>
    </div>
  );
}

export default Edit;
