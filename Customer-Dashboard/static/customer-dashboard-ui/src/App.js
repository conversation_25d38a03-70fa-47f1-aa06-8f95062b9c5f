import React, { useEffect, useState, Suspense } from 'react';
import { view } from '@forge/bridge';
import View from './View';
import Edit from './Edit';
import Config from './Config';

// Loading component for better user experience
const Loading = () => (
  <div style={{
    padding: '20px',
    textAlign: 'center',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif',
    color: '#172B4D'
  }}>
    Loading...
  </div>
);

function App() {
  const [context, setContext] = useState();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getContext = async () => {
      try {
        const contextData = await view.getContext();
        setContext(contextData);
      } catch (error) {
        console.error('Error fetching context:', error);
      } finally {
        setLoading(false);
      }
    };

    getContext();
  }, []);

  if (loading || !context) {
    return <Loading />;
  }

  // Determine which component to render based on the entry point
  const entryPoint = context.extension.entryPoint;

  return (
    <Suspense fallback={<Loading />}>
      {entryPoint === 'edit' ? <Edit /> : <View />}
    </Suspense>
  );
}

export default App;
