import React, { useEffect, useState } from 'react';
import { view, invoke } from '@forge/bridge';
import './Config.css';

function Config() {
  const [context, setContext] = useState();
  const [filterId, setFilterId] = useState('');
  const [showOverall, setShowOverall] = useState(true);
  const [highlightColor, setHighlightColor] = useState('#0052CC');
  const [uiStyle, setUiStyle] = useState('modern');
  const [csmColumnLabel, setCsmColumnLabel] = useState('CSM Assigned');
  const [fundedByColumnLabel, setFundedByColumnLabel] = useState('Funded By');
  const [showFundedByColumn, setShowFundedByColumn] = useState(true);
  const [salesContactColumnLabel, setSalesContactColumnLabel] = useState('Sales Contact');
  const [showSalesContactColumn, setShowSalesContactColumn] = useState(true);
  const [showCsmColumn, setShowCsmColumn] = useState(true);
  const [sreContactColumnLabel, setSreContactColumnLabel] = useState('SRE Contact');
  const [showSreContactColumn, setShowSreContactColumn] = useState(true);
  const [dueDateColumnLabel, setDueDateColumnLabel] = useState('Due Date');
  const [showDueDateColumn, setShowDueDateColumn] = useState(true);
  const [showTasksColumns, setShowTasksColumns] = useState(true);
  const [savedFilters, setSavedFilters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false);

  // Fetch saved filters and load existing configuration
  useEffect(() => {
    const initialize = async () => {
      try {
        setLoading(true);

        // Get context for current configuration
        const contextData = await view.getContext();
        setContext(contextData);

        // Load existing configuration if available
        const config = contextData?.extension?.gadgetConfiguration || {};

        if (config.filterId !== undefined) {
          setFilterId(config.filterId);
        }

        if (config.showOverall !== undefined) {
          setShowOverall(config.showOverall);
        }

        if (config.highlightColor !== undefined) {
          setHighlightColor(config.highlightColor);
        }

        if (config.uiStyle !== undefined) {
          setUiStyle(config.uiStyle);
        }

        if (config.csmColumnLabel !== undefined) {
          setCsmColumnLabel(config.csmColumnLabel);
        }

        if (config.fundedByColumnLabel !== undefined) {
          setFundedByColumnLabel(config.fundedByColumnLabel);
        }

        if (config.showFundedByColumn !== undefined) {
          setShowFundedByColumn(config.showFundedByColumn);
        }

        if (config.salesContactColumnLabel !== undefined) {
          setSalesContactColumnLabel(config.salesContactColumnLabel);
        }

        if (config.showSalesContactColumn !== undefined) {
          setShowSalesContactColumn(config.showSalesContactColumn);
        }

        if (config.showCsmColumn !== undefined) {
          setShowCsmColumn(config.showCsmColumn);
        }

        if (config.sreContactColumnLabel !== undefined) {
          setSreContactColumnLabel(config.sreContactColumnLabel);
        }

        if (config.showSreContactColumn !== undefined) {
          setShowSreContactColumn(config.showSreContactColumn);
        }

        if (config.dueDateColumnLabel !== undefined) {
          setDueDateColumnLabel(config.dueDateColumnLabel);
        }

        if (config.showDueDateColumn !== undefined) {
          setShowDueDateColumn(config.showDueDateColumn);
        }

        if (config.showTasksColumns !== undefined) {
          setShowTasksColumns(config.showTasksColumns);
        }

        // Fetch saved filters
        const filters = await invoke('getSavedFilters');

        // Transform filters for display
        const filterOptions = filters.map(filter => ({
          id: filter.id,
          name: filter.name,
          description: filter.description || '',
          jql: filter.jql
        }));

        setSavedFilters(filterOptions);
      } catch (error) {
        console.error('Error initializing configuration:', error);
      } finally {
        setLoading(false);
      }
    };

    initialize();
  }, []);

  const saveConfig = async () => {
    setSaving(true);

    try {
      await view.setGadgetConfiguration({
        filterId,
        showOverall,
        highlightColor,
        uiStyle,
        csmColumnLabel,
        showCsmColumn,
        fundedByColumnLabel,
        showFundedByColumn,
        salesContactColumnLabel,
        showSalesContactColumn,
        sreContactColumnLabel,
        showSreContactColumn,
        dueDateColumnLabel,
        showDueDateColumn,
        showTasksColumns
      });

      setSaved(true);
      setTimeout(() => setSaved(false), 3000);
    } catch (error) {
      console.error('Error saving configuration:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="config-container">
      <h2>Customer Progress Configuration</h2>

      <div className="config-form">
        {loading ? (
          <div className="loading">Loading configuration...</div>
        ) : (
          <>
            <div className="form-group">
              <label htmlFor="filterId">Saved Filter:</label>
              <select
                id="filterId"
                value={filterId}
                onChange={(e) => setFilterId(e.target.value)}
                className="filter-select"
              >
                <option value="">Select a filter</option>
                {savedFilters.map(filter => (
                  <option key={filter.id} value={filter.id}>
                    {filter.name}
                  </option>
                ))}
              </select>
              <div className="field-description">
                Select a saved filter that contains epics to display
              </div>
            </div>

            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={showOverall}
                  onChange={(e) => setShowOverall(e.target.checked)}
                />
                Show Overall Progress
              </label>
              <div className="field-description">
                Display overall progress percentage across all customers
              </div>
            </div>

            <h3 className="section-header">Column Visibility and Labels</h3>

            <div className="form-group">
              <div className="column-config">
                <div className="column-visibility">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={showFundedByColumn}
                      onChange={(e) => setShowFundedByColumn(e.target.checked)}
                    />
                    Show Funded By Column
                  </label>
                </div>
                <div className="column-label">
                  <label htmlFor="fundedByColumnLabel">Label:</label>
                  <input
                    id="fundedByColumnLabel"
                    type="text"
                    value={fundedByColumnLabel}
                    onChange={(e) => setFundedByColumnLabel(e.target.value)}
                    className="text-input"
                    placeholder="Enter column label"
                    disabled={!showFundedByColumn}
                  />
                </div>
              </div>
              <div className="field-description">
                Customize the visibility and label for the Funded By column
              </div>
            </div>

            <div className="form-group">
              <div className="column-config">
                <div className="column-visibility">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={showSalesContactColumn}
                      onChange={(e) => setShowSalesContactColumn(e.target.checked)}
                    />
                    Show Sales Contact Column
                  </label>
                </div>
                <div className="column-label">
                  <label htmlFor="salesContactColumnLabel">Label:</label>
                  <input
                    id="salesContactColumnLabel"
                    type="text"
                    value={salesContactColumnLabel}
                    onChange={(e) => setSalesContactColumnLabel(e.target.value)}
                    className="text-input"
                    placeholder="Enter column label"
                    disabled={!showSalesContactColumn}
                  />
                </div>
              </div>
              <div className="field-description">
                Customize the visibility and label for the Sales Contact column
              </div>
            </div>

            <div className="form-group">
              <div className="column-config">
                <div className="column-visibility">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={showCsmColumn}
                      onChange={(e) => setShowCsmColumn(e.target.checked)}
                    />
                    Show CSM Column
                  </label>
                </div>
                <div className="column-label">
                  <label htmlFor="csmColumnLabel">Label:</label>
                  <input
                    id="csmColumnLabel"
                    type="text"
                    value={csmColumnLabel}
                    onChange={(e) => setCsmColumnLabel(e.target.value)}
                    className="text-input"
                    placeholder="Enter column label"
                    disabled={!showCsmColumn}
                  />
                </div>
              </div>
              <div className="field-description">
                Customize the visibility and label for the CSM Assigned column
              </div>
            </div>

            <div className="form-group">
              <div className="column-config">
                <div className="column-visibility">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={showSreContactColumn}
                      onChange={(e) => setShowSreContactColumn(e.target.checked)}
                    />
                    Show SRE Contact Column
                  </label>
                </div>
                <div className="column-label">
                  <label htmlFor="sreContactColumnLabel">Label:</label>
                  <input
                    id="sreContactColumnLabel"
                    type="text"
                    value={sreContactColumnLabel}
                    onChange={(e) => setSreContactColumnLabel(e.target.value)}
                    className="text-input"
                    placeholder="Enter column label"
                    disabled={!showSreContactColumn}
                  />
                </div>
              </div>
              <div className="field-description">
                Customize the visibility and label for the SRE Contact column
              </div>
            </div>

            <div className="form-group">
              <div className="column-config">
                <div className="column-visibility">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={showDueDateColumn}
                      onChange={(e) => setShowDueDateColumn(e.target.checked)}
                    />
                    Show Due Date Column
                  </label>
                </div>
                <div className="column-label">
                  <label htmlFor="dueDateColumnLabel">Label:</label>
                  <input
                    id="dueDateColumnLabel"
                    type="text"
                    value={dueDateColumnLabel}
                    onChange={(e) => setDueDateColumnLabel(e.target.value)}
                    className="text-input"
                    placeholder="Enter column label"
                    disabled={!showDueDateColumn}
                  />
                </div>
              </div>
              <div className="field-description">
                Customize the visibility and label for the Due Date column
              </div>
            </div>

            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={showTasksColumns}
                  onChange={(e) => setShowTasksColumns(e.target.checked)}
                />
                Show Tasks Columns
              </label>
              <div className="field-description">
                Show or hide the All Tasks and Completed Tasks columns
              </div>
            </div>

            <div className="form-actions">
              <button
                className="save-button"
                onClick={saveConfig}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save'}
              </button>

              {saved && (
                <div className="save-success">
                  Configuration saved successfully!
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default Config;
