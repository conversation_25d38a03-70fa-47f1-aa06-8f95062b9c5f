.edit-container {
  --highlight-color: #0052CC;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  padding: 24px;
  max-width: 650px;
  margin: 0 auto;
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(9, 30, 66, 0.15);
}

.edit-container h2 {
  margin-bottom: 16px;
  color: var(--highlight-color, #172B4D);
  font-size: 24px;
  font-weight: 500;
  border-bottom: 2px solid #F4F5F7;
  padding-bottom: 16px;
}

.description {
  margin-bottom: 28px;
  color: #5E6C84;
  font-size: 15px;
  line-height: 1.5;
  background-color: #F4F5F7;
  padding: 12px 16px;
  border-radius: 8px;
  border-left: 4px solid var(--highlight-color, #0052CC);
}

.checkbox-container {
  display: flex;
  align-items: center;
  margin-top: 12px;
  padding: 10px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.checkbox-container:hover {
  background-color: #F4F5F7;
}

.checkbox-container input[type="checkbox"] {
  margin-right: 10px;
  cursor: pointer;
  width: 18px;
  height: 18px;
  accent-color: var(--highlight-color, #0052CC);
}

.checkbox-label {
  font-size: 15px;
  color: #172B4D;
  cursor: pointer;
  font-weight: 500;
}

/* Color picker styles */
.color-picker-container {
  margin-top: 16px;
  padding: 16px;
  background-color: #F8F9FA;
  border-radius: 8px;
  border: 1px solid #DFE1E6;
}

.color-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2px 4px rgba(9, 30, 66, 0.1);
}

.color-option:hover {
  transform: scale(1.15);
  box-shadow: 0 4px 8px rgba(9, 30, 66, 0.25);
}

.color-option.selected {
  box-shadow: 0 0 0 3px var(--highlight-color, #0052CC), 0 2px 4px rgba(9, 30, 66, 0.2);
}

.color-option.selected::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 16px;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
}

.color-option[style*="background-color: #FFFFFF"].selected::after {
  color: #172B4D;
  text-shadow: none;
}

.selected-color-name {
  font-size: 15px;
  color: #5E6C84;
  margin-top: 8px;
  font-weight: 500;
  padding: 6px 10px;
  background-color: white;
  border-radius: 4px;
  display: inline-block;
}

/* UI Style selector */
.ui-style-container {
  margin-top: 12px;
}

/* Form actions */
.form-actions {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #F4F5F7;
}

.style-options-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.style-option {
  display: flex;
  flex-direction: column;
  padding: 16px;
  border: 2px solid #DFE1E6;
  border-radius: 8px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(9, 30, 66, 0.1);
}

.style-option:hover {
  background-color: #F4F5F7;
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(9, 30, 66, 0.15);
}

.style-option.selected {
  border-color: var(--highlight-color, #0052CC);
  background-color: rgba(0, 82, 204, 0.05);
}

.style-option-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.style-option input[type="checkbox"] {
  margin-right: 10px;
  cursor: pointer;
  width: 18px;
  height: 18px;
  accent-color: var(--highlight-color, #0052CC);
}

.style-option label {
  font-weight: 600;
  font-size: 16px;
  color: #172B4D;
  cursor: pointer;
}

.style-description {
  font-size: 14px;
  color: #5E6C84;
  margin-top: 4px;
  padding-left: 28px;
}

/* Column configuration styles */
.section-header {
  font-size: 18px;
  font-weight: 500;
  color: #172B4D;
  margin-top: 24px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #DFE1E6;
}

.columns-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #F8F9FA;
  border-radius: 8px;
  border: 1px solid #DFE1E6;
}

.field-description {
  font-size: 13px;
  color: #6B778C;
  margin-top: 4px;
}
