.config-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  padding: 20px;
  max-width: 100%;
  color: #172B4D;
}

.config-container h2 {
  margin-bottom: 20px;
  color: #172B4D;
  font-size: 20px;
  font-weight: 500;
  border-bottom: 1px solid #DFE1E6;
  padding-bottom: 12px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  font-size: 14px;
  color: #172B4D;
}

.form-group input[type="text"] {
  padding: 8px 12px;
  border: 2px solid #DFE1E6;
  border-radius: 3px;
  font-size: 14px;
  width: 100%;
  max-width: 300px;
}

.form-group input[type="text"]:focus {
  border-color: #4C9AFF;
  outline: none;
  box-shadow: 0 0 0 2px rgba(76, 154, 255, 0.3);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  cursor: pointer;
  width: 16px;
  height: 16px;
}

.field-description {
  font-size: 12px;
  color: #6B778C;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
}

.save-button {
  background-color: #0052CC;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-button:hover {
  background-color: #0065FF;
}

.save-button:disabled {
  background-color: #091E42;
  opacity: 0.5;
  cursor: not-allowed;
}

.save-success {
  color: #00875A;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.save-success::before {
  content: "✓";
  margin-right: 8px;
  font-weight: bold;
}

.section-header {
  font-size: 16px;
  font-weight: 500;
  color: #172B4D;
  margin-top: 20px;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #DFE1E6;
}

.column-config {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.column-visibility {
  flex: 1;
  min-width: 200px;
}

.column-label {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 2;
  min-width: 250px;
}

.column-label input {
  flex: 1;
}

.column-label label {
  white-space: nowrap;
}

/* Disabled input styling */
.form-group input[type="text"]:disabled {
  background-color: #F4F5F7;
  color: #A5ADBA;
  cursor: not-allowed;
}
