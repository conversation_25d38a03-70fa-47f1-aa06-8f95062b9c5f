/* Main container styles */
.epic-progress-tracker {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  padding: 24px;
  max-width: 100%;
  background-color: #FFFFFF;
  background-image: linear-gradient(to bottom, #FFFFFF, #FAFBFC);
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(9, 30, 66, 0.15);
  transition: box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;

  /* Default highlight color (will be overridden by inline style) */
  --highlight-color: #0052CC;
}

.epic-progress-tracker:hover {
  box-shadow: 0 4px 15px rgba(9, 30, 66, 0.2);
}

/* Header styles */
.epic-progress-tracker h2 {
  margin-bottom: 24px;
  color: #172B4D;
  font-size: 22px;
  font-weight: 600;
  border-bottom: 2px solid #DFE1E6;
  padding-bottom: 14px;
  position: relative;
}

.epic-progress-tracker h2::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background-color: var(--highlight-color);
}

/* Overall progress section */
.overall-progress-section {
  margin-bottom: 28px;
  padding: 20px;
  background-color: #F4F5F7;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
  border-radius: 8px;
  border-left: 5px solid var(--highlight-color);
  box-shadow: 0 3px 8px rgba(9, 30, 66, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.overall-progress-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(9, 30, 66, 0.15);
}

.overall-progress-section::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100%;
  background: linear-gradient(to left, rgba(255, 255, 255, 0.4), transparent);
  pointer-events: none;
}

.overall-progress-title {
  font-size: 18px;
  font-weight: 600;
  color: #172B4D;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(9, 30, 66, 0.1);
  padding-bottom: 10px;
}

.overall-progress-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.overall-progress-percentage {
  font-size: 32px;
  font-weight: 700;
  color: var(--highlight-color);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: color 0.3s ease;
}

.overall-progress-details {
  font-size: 14px;
  color: #5E6C84;
  background-color: rgba(255, 255, 255, 0.6);
  padding: 6px 12px;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(9, 30, 66, 0.08);
}

/* Table styles */
.epic-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 20px;
  border-radius: 8px;
  overflow-x: auto; /* Enable horizontal scrolling if needed */
  background-color: white;
  table-layout: auto; /* Dynamic sizing */
}

/* Add a container for the table to enable horizontal scrolling on small screens */
.epic-table-container {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 20px;
  border-radius: 8px;
  /* Remove the box shadow */
  /* Ensure the container can handle the expanded content */
  min-width: 100%;
  display: block;
}

.epic-table th {
  text-align: left;
  padding: 14px 24px;
  background-color: #F4F5F7;
  border-bottom: 1px solid #DFE1E6;
  color: #172B4D;
  font-weight: 600;
  font-size: 14px;
  position: relative;
}

/* Sortable header styles */
.sortable-header {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.sortable-header:hover {
  background-color: #EBECF0;
}

.sortable-header.sorted {
  background-color: #DEEBFF;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.sort-indicator {
  font-size: 10px;
  color: #0052CC;
  display: inline-block;
  width: 12px;
  text-align: center;
}

/* Add a subtle styling to the header row */
.epic-table thead tr.table-header-row {
  background: linear-gradient(to bottom, #F4F5F7, #EBECF0);
  border-bottom: 2px solid #DFE1E6;
}

/* Remove the shadow/gap below the header */
.epic-table thead:after {
  display: none;
}

.epic-table td, .epic-table th {
  padding: 0;
  border-bottom: 1px solid #DFE1E6;
  color: #172B4D;
  font-size: 14px;
  vertical-align: middle;
}

/* Cell content wrapper */
.cell-content {
  padding: 14px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 80px;
  box-sizing: border-box;
  overflow: hidden; /* Prevent content from overflowing */
}

.cell-content-center {
  justify-content: center;
  text-align: center;
}

/* Special handling for assignee cell to ensure full visibility */
.assignee-cell {
  overflow: visible;
  white-space: nowrap;
  min-width: 150px;
  width: auto;
  justify-content: center;
}

/* Add text truncation for most cells, except assignee */
.cell-content > *:not(.assignee-name) {
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Dynamic column widths - already set in the table styles */

/* Customer name column - give it more space but allow it to shrink */
.epic-table th:first-child,
.epic-table td:first-child {
  min-width: 180px;
  max-width: 35%;
  width: auto;
}

/* Funded By column - compact */
.epic-table th:nth-child(2),
.epic-table td:nth-child(2) {
  min-width: 100px;
  width: auto;
}

/* Sales Contact column - compact */
.epic-table th:nth-child(3),
.epic-table td:nth-child(3) {
  min-width: 120px;
  width: auto;
}

/* CSM Assigned column - allow to grow based on name length */
.epic-table th:nth-child(4),
.epic-table td:nth-child(4) {
  min-width: 150px;
  width: auto;
  white-space: nowrap;
  overflow: visible;
}

/* SRE Contact column - compact */
.epic-table th:nth-child(5),
.epic-table td:nth-child(5) {
  min-width: 120px;
  width: auto;
}

/* Due Date column - compact */
.epic-table th:nth-child(6),
.epic-table td:nth-child(6) {
  min-width: 100px;
  width: auto;
}

/* Task count columns - keep compact */
.epic-table th:nth-child(7),
.epic-table td:nth-child(7),
.epic-table th:nth-child(8),
.epic-table td:nth-child(8) {
  min-width: 80px;
  width: auto;
}

/* Progress column - give it enough space for the progress bar */
.epic-table th:last-child,
.epic-table td:last-child {
  min-width: 150px;
  width: auto;
}

/* Field value style for custom fields */
.field-value {
  display: inline-block;
  font-size: 13px;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: #F4F5F7;
  color: #42526E;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(9, 30, 66, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  transition: all 0.2s ease;
}

.field-value:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(9, 30, 66, 0.2);
}

.field-value.classic {
  background-color: #F4F5F7;
  color: #172B4D;
  box-shadow: none;
  padding: 2px 6px;
  font-weight: normal;
  transition: none;
}

.field-value.classic:hover {
  transform: none;
  box-shadow: none;
}

.epic-table tr:last-child td {
  border-bottom: none;
}

.epic-table tr:hover {
  background-color: #F8F9FA;
}

/* Zebra striping for better readability */
.epic-table tbody tr:nth-child(even) {
  background-color: #FAFBFC;
}

.epic-table tbody tr:hover {
  background-color: rgba(9, 30, 66, 0.04);
  transition: background-color 0.2s ease;
}

/* Epic name column */
.epic-name {
  font-weight: 500;
  color: var(--highlight-color);
  cursor: pointer;
  text-decoration: none;
  flex-grow: 1;
  white-space: normal;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
  width: 100%; /* Use full width of container */
  min-width: 0; /* Allow text to shrink below content size */
}

.epic-name:hover {
  text-decoration: underline;
}

.epic-status {
  display: inline-block;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 4px;
  background-color: #DFE1E6;
  color: #42526E;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(9, 30, 66, 0.1);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.epic-status.done {
  background-color: #E3FCEF;
  color: #006644;
  border-left: 3px solid #36B37E;
}

.epic-status.in-progress {
  background-color: #DEEBFF;
  color: #0747A6;
  border-left: 3px solid var(--highlight-color);
}

/* Progress bar styles */
.progress-bar-container {
  width: 100%;
  background-color: #EBECF0;
  border-radius: 6px;
  height: 10px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(9, 30, 66, 0.1);
  position: relative;
  flex-grow: 1;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--highlight-color);
  border-radius: 6px;
  transition: width 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
}

.progress-bar-fill.complete {
  background-color: #36B37E;
  box-shadow: 0 0 6px rgba(54, 179, 126, 0.5);
}

.progress-bar-fill.in-progress {
  background-color: var(--highlight-color);
  box-shadow: 0 0 6px rgba(0, 82, 204, 0.3);
}

.progress-bar-fill.not-started {
  background-color: #6B778C;
}

/* Animation removed for static progress bar */

.progress-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.progress-percentage {
  font-size: 15px;
  font-weight: 600;
  margin-top: 8px;
  color: #172B4D;
  transition: color 0.3s ease;
}

.days-display {
  font-size: 13px;
  font-weight: 400;
  color: #5E6C84;
  margin-left: 4px;
}

/* Status indicators */
.count-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  background-color: #DFE1E6;
  color: #42526E;
  box-shadow: 0 1px 2px rgba(9, 30, 66, 0.1);
  min-width: 24px;
  text-align: center;
  transition: transform 0.2s ease;
}

.count-badge:hover {
  transform: scale(1.05);
}

.count-badge.completed {
  background-color: #E3FCEF;
  color: #006644;
  border: 1px solid rgba(54, 179, 126, 0.3);
}

/* Assignee name styles - now matching field-value */
.assignee-name {
  display: inline-block;
  font-size: 13px;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: #F4F5F7;
  color: #42526E;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(9, 30, 66, 0.1);
  white-space: nowrap;
  transition: all 0.2s ease;
  position: relative;
  /* Remove width constraints and text truncation */
  width: auto;
  overflow: visible;
  text-overflow: ellipsis;
  max-width: 100%;
}

.assignee-name:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(9, 30, 66, 0.2);
}

/* Classic UI styles */
.epic-progress-tracker.classic {
  background-image: none;
  background-color: #FFFFFF;
  box-shadow: 0 1px 2px rgba(9, 30, 66, 0.1);
  border-radius: 3px;
  padding: 16px;
}

.epic-progress-tracker.classic h2 {
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #DFE1E6;
  padding-bottom: 10px;
  margin-bottom: 16px;
}

.epic-progress-tracker.classic h2::after {
  display: none;
}

.overall-progress-section.classic {
  background-image: none;
  background-color: #F4F5F7;
  box-shadow: none;
  border-radius: 3px;
  border-left: 1px solid #DFE1E6;
  padding: 12px;
  margin-bottom: 16px;
  transition: none;
}

.overall-progress-section.classic:hover {
  transform: none;
  box-shadow: none;
}

.overall-progress-section.classic::after {
  display: none;
}

.overall-progress-title.classic {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
  border-bottom: none;
  padding-bottom: 0;
}

.overall-progress-stats.classic {
  margin-bottom: 10px;
}

.overall-progress-percentage.classic {
  font-size: 20px;
  font-weight: 500;
  text-shadow: none;
}

.overall-progress-details.classic {
  background-color: transparent;
  box-shadow: none;
  padding: 0;
  border-radius: 0;
}

.progress-wrapper.classic {
  margin-top: 5px;
}

.progress-bar-container.classic {
  height: 8px;
  border-radius: 3px;
  box-shadow: none;
}

.progress-bar-fill.classic {
  border-radius: 3px;
  box-shadow: none;
  transition: width 0.3s ease;
}

.progress-percentage.classic {
  font-size: 12px;
  font-weight: normal;
}

.progress-percentage.classic .days-display {
  font-size: 11px;
  color: #6B778C;
}

.epic-name.classic {
  color: #0052CC !important;
  font-weight: normal;
  -webkit-line-clamp: 2;
  line-height: 1.3;
}

.count-badge.classic {
  background-color: #F4F5F7;
  box-shadow: none;
  padding: 2px 6px;
  font-weight: normal;
  transition: none;
}

.count-badge.classic:hover {
  transform: none;
}

.count-badge.classic.completed {
  background-color: #F4F5F7;
  color: #172B4D;
  border: none;
}

.assignee-name.classic {
  background-color: #F4F5F7;
  color: #172B4D;
  box-shadow: none;
  padding: 2px 6px;
  font-weight: normal;
  transition: none;
}

.assignee-name.classic:hover {
  transform: none;
  box-shadow: none;
}

.epic-status.classic {
  box-shadow: none;
  padding: 2px 6px;
  font-weight: normal;
  border-left: none;
  transition: none;
}

.epic-status.classic.done {
  background-color: #E3FCEF;
  color: #006644;
  border-left: none;
}

.epic-status.classic.in-progress {
  background-color: #DEEBFF;
  color: #0747A6;
  border-left: none;
  border-bottom: 2px solid var(--highlight-color);
}

.classic-row:hover {
  background-color: #F4F5F7;
}

/* Filter and Epic selectors */
.filter-selection-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background-color: #F4F5F7;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(9, 30, 66, 0.1);
}

.filter-selector, .epic-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 250px;
}

.filter-selector label, .epic-selector label {
  font-weight: 500;
  color: #172B4D;
  white-space: nowrap;
}

.filter-selector select, .epic-selector select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #DFE1E6;
  border-radius: 4px;
  background-color: white;
  color: #172B4D;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  min-width: 200px;
}

.filter-selector select:hover, .epic-selector select:hover {
  border-color: #B3BAC5;
}

.filter-selector select:focus, .epic-selector select:focus {
  outline: none;
  border-color: var(--highlight-color);
  box-shadow: 0 0 0 2px rgba(0, 82, 204, 0.2);
}

.filter-selector select:disabled, .epic-selector select:disabled {
  background-color: #F4F5F7;
  cursor: not-allowed;
  opacity: 0.7;
}

.filter-selector.classic select, .epic-selector.classic select {
  border-radius: 3px;
  padding: 6px 10px;
  font-size: 13px;
  transition: none;
}

.filter-selector.classic select:focus, .epic-selector.classic select:focus {
  box-shadow: none;
}

.loading-indicator {
  display: inline-block;
  font-size: 13px;
  color: #5E6C84;
  margin-left: 8px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* Loading, error, and empty states */
.loading, .error, .no-data {
  padding: 40px 20px;
  text-align: center;
  color: #172B4D;
  background-color: #F4F5F7;
  border-radius: 6px;
  margin: 20px 0;
}

.loading-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  border: 2px solid #DFE1E6;
  border-radius: 50%;
  border-top-color: var(--highlight-color);
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error {
  color: #DE350B;
  background-color: #FFEBE6;
  border-left: 4px solid #DE350B;
  padding: 20px;
  text-align: left;
  box-shadow: 0 2px 8px rgba(222, 53, 11, 0.2);
}

.error h3 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #DE350B;
}

.error p {
  margin-bottom: 12px;
  line-height: 1.5;
}

/* Calculation Info - explicitly hidden */
.calculation-info {
  display: none !important;
}

.no-data {
  color: #5E6C84;
  font-style: italic;
}

/* Responsive adjustments for smaller screens */
@media screen and (max-width: 1200px) {
  /* Adjust padding for smaller screens */
  .cell-content {
    padding: 10px 12px;
  }

  /* Reduce font size slightly */
  .epic-table {
    font-size: 13px;
  }

  /* Ensure minimum widths are appropriate */
  .epic-table th:first-child,
  .epic-table td:first-child {
    min-width: 150px;
  }

  /* Compact columns */
  .epic-table th:nth-child(2),
  .epic-table td:nth-child(2),
  .epic-table th:nth-child(3),
  .epic-table td:nth-child(3),
  .epic-table th:nth-child(5),
  .epic-table td:nth-child(5),
  .epic-table th:nth-child(6),
  .epic-table td:nth-child(6),
  .epic-table th:nth-child(7),
  .epic-table td:nth-child(7),
  .epic-table th:nth-child(8),
  .epic-table td:nth-child(8) {
    min-width: 70px;
  }

  /* Maintain CSM Assigned column width */
  .epic-table th:nth-child(4),
  .epic-table td:nth-child(4) {
    min-width: 150px;
  }
}

@media screen and (max-width: 768px) {
  /* Further reduce padding */
  .cell-content {
    padding: 8px 10px;
    gap: 5px;
  }

  /* Make sure horizontal scrolling works well */
  .epic-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Ensure the table doesn't shrink too much */
  .epic-table {
    min-width: 1050px; /* Increased to accommodate all columns */
  }

  /* Maintain CSM Assigned column width even on smallest screens */
  .epic-table th:nth-child(4),
  .epic-table td:nth-child(4),
  .assignee-cell {
    min-width: 150px;
    overflow: visible;
  }
}
