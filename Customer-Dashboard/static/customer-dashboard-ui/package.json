{"name": "customer-dashboard-ui", "version": "0.1.46", "private": true, "homepage": ".", "dependencies": {"@atlaskit/button": "^17.3.2", "@atlaskit/css-reset": "^6.6.2", "@atlaskit/form": "^9.0.10", "@atlaskit/progress-bar": "^2.0.0", "@atlaskit/select": "^16.7.6", "@atlaskit/table": "^0.11.0", "@atlaskit/textfield": "^6.0.1", "@forge/bridge": "4.5.2", "ajv": "^8.17.1", "react": "^16", "react-dom": "^16"}, "devDependencies": {"react-scripts": "^5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}